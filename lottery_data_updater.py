"""
大乐透数据更新器
================

从体彩官方API获取真实开奖数据并保存为CSV格式
API地址: https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry

数据格式:
- 期号 (lotteryDrawNum)
- 开奖日期 (lotteryDrawTime) 
- 中奖号码 (lotteryDrawResult)

支持功能:
1. 获取最新200条记录
2. 智能增量更新
3. 数据验证和清洗
4. CSV格式保存
"""

import requests
import pandas as pd
import json
from datetime import datetime
import os
import time

class LotteryDataUpdater:
    """大乐透数据更新器"""
    
    def __init__(self, csv_file="lottery_data.csv"):
        self.api_url = "https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry"
        self.csv_file = csv_file
        self.params = {
            'gameNo': '85',        # 大乐透游戏编号
            'provinceId': '0',     # 全国
            'pageSize': '200',     # 每页200条记录
            'isVerify': '1',       # 已验证
            'pageNo': '1'          # 页码
        }
    
    def fetch_lottery_data(self, page_no=1):
        """
        从官方API获取开奖数据
        
        Args:
            page_no (int): 页码，默认第1页
            
        Returns:
            dict: API响应数据
        """
        try:
            params = self.params.copy()
            params['pageNo'] = str(page_no)
            
            print(f"正在获取第{page_no}页数据...")
            response = requests.get(self.api_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('success') and data.get('errorCode') == '0':
                print(f"✅ 成功获取数据，共{len(data['value']['list'])}条记录")
                return data
            else:
                print(f"❌ API返回错误: {data.get('errorMessage', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return None
    
    def parse_lottery_numbers(self, draw_result):
        """
        解析开奖号码字符串
        
        Args:
            draw_result (str): 开奖号码字符串，如 "02 05 09 14 33 04 09"
            
        Returns:
            dict: 解析后的号码数据
        """
        numbers = draw_result.strip().split()
        
        if len(numbers) != 7:
            print(f"⚠️ 号码格式异常: {draw_result}")
            return None
        
        return {
            'front_1': int(numbers[0]),
            'front_2': int(numbers[1]),
            'front_3': int(numbers[2]),
            'front_4': int(numbers[3]),
            'front_5': int(numbers[4]),
            'back_1': int(numbers[5]),
            'back_2': int(numbers[6])
        }
    
    def process_raw_data(self, raw_data):
        """
        处理原始API数据，转换为标准格式
        
        Args:
            raw_data (dict): API返回的原始数据
            
        Returns:
            list: 处理后的数据列表
        """
        if not raw_data or 'value' not in raw_data or 'list' not in raw_data['value']:
            return []
        
        processed_data = []
        
        for item in raw_data['value']['list']:
            # 解析号码
            numbers = self.parse_lottery_numbers(item['lotteryDrawResult'])
            if not numbers:
                continue
            
            # 构建记录
            record = {
                'period': item['lotteryDrawNum'],
                'draw_date': item['lotteryDrawTime'],
                'draw_result': item['lotteryDrawResult'],
                **numbers
            }
            
            processed_data.append(record)
        
        return processed_data
    
    def load_existing_data(self):
        """
        加载现有CSV数据
        
        Returns:
            pd.DataFrame: 现有数据，如果文件不存在返回空DataFrame
        """
        if os.path.exists(self.csv_file):
            try:
                df = pd.read_csv(self.csv_file)
                print(f"📁 加载现有数据: {len(df)}条记录")
                return df
            except Exception as e:
                print(f"⚠️ 加载现有数据失败: {e}")
                return pd.DataFrame()
        else:
            print("📁 未找到现有数据文件，将创建新文件")
            return pd.DataFrame()
    
    def merge_and_deduplicate(self, existing_df, new_data):
        """
        合并新旧数据并去重
        
        Args:
            existing_df (pd.DataFrame): 现有数据
            new_data (list): 新获取的数据
            
        Returns:
            pd.DataFrame: 合并后的数据
        """
        if not new_data:
            return existing_df
        
        new_df = pd.DataFrame(new_data)
        
        if existing_df.empty:
            merged_df = new_df
        else:
            # 合并数据
            merged_df = pd.concat([existing_df, new_df], ignore_index=True)
        
        # 按期号去重，保留最新的记录
        merged_df = merged_df.drop_duplicates(subset=['period'], keep='last')
        
        # 按期号排序（降序，最新的在前）
        merged_df = merged_df.sort_values('period', ascending=False)
        
        print(f"🔄 数据合并完成: {len(merged_df)}条记录")
        return merged_df
    
    def save_to_csv(self, df):
        """
        保存数据到CSV文件
        
        Args:
            df (pd.DataFrame): 要保存的数据
        """
        try:
            # 重新排列列的顺序
            columns_order = [
                'period', 'draw_date', 'draw_result',
                'front_1', 'front_2', 'front_3', 'front_4', 'front_5',
                'back_1', 'back_2'
            ]
            
            df = df[columns_order]
            df.to_csv(self.csv_file, index=False, encoding='utf-8-sig')
            print(f"💾 数据已保存到: {self.csv_file}")
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def update_data(self, max_pages=1):
        """
        更新数据主函数
        
        Args:
            max_pages (int): 最大获取页数，默认1页(200条记录)
        """
        print("🚀 开始更新大乐透数据...")
        print(f"📊 API地址: {self.api_url}")
        print(f"📁 保存文件: {self.csv_file}")
        print("-" * 50)
        
        # 加载现有数据
        existing_df = self.load_existing_data()
        
        all_new_data = []
        
        # 获取多页数据
        for page in range(1, max_pages + 1):
            raw_data = self.fetch_lottery_data(page)
            if raw_data:
                new_data = self.process_raw_data(raw_data)
                all_new_data.extend(new_data)
                
                # 如果不是最后一页，稍作延迟避免请求过快
                if page < max_pages:
                    time.sleep(1)
            else:
                print(f"⚠️ 第{page}页数据获取失败，跳过")
        
        if not all_new_data:
            print("❌ 未获取到任何新数据")
            return
        
        # 合并数据
        final_df = self.merge_and_deduplicate(existing_df, all_new_data)
        
        # 保存数据
        self.save_to_csv(final_df)
        
        # 显示统计信息
        self.show_statistics(final_df)
        
        print("✅ 数据更新完成！")
    
    def show_statistics(self, df):
        """
        显示数据统计信息
        
        Args:
            df (pd.DataFrame): 数据
        """
        if df.empty:
            return
        
        print("\n📈 数据统计:")
        print(f"总记录数: {len(df)}")
        print(f"最新期号: {df.iloc[0]['period']}")
        print(f"最早期号: {df.iloc[-1]['period']}")
        print(f"最新开奖: {df.iloc[0]['draw_date']}")
        print(f"最早开奖: {df.iloc[-1]['draw_date']}")
        
        # 显示最近5期
        print("\n🎯 最近5期开奖:")
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            print(f"  {row['period']}: {row['draw_result']} ({row['draw_date']})")

def main():
    """主函数"""
    updater = LotteryDataUpdater()
    
    # 更新数据（获取最新200条记录）
    updater.update_data(max_pages=1)
    
    print("\n" + "="*60)
    print("🎲 大乐透数据更新器使用说明:")
    print("1. 运行此脚本自动获取最新200条开奖记录")
    print("2. 数据保存在 lottery_data.csv 文件中")
    print("3. 支持增量更新，重复运行会自动去重")
    print("4. 数据格式: 期号、开奖日期、中奖号码、前区5个号码、后区2个号码")
    print("="*60)

if __name__ == "__main__":
    main()
