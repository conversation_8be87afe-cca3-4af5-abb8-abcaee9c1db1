"""
彩票分析方法复刻版
=================

本代码复刻自以下真实项目的标准算法：

1. GitHub项目: szczyglis-dev/python-lottery-dataset-analyze
   来源: https://github.com/szczyglis-dev/python-lottery-dataset-analyze
   
2. Medium文章: "Analyzing Lottery Numbers: Finding Patterns in the Data"
   作者: <PERSON><PERSON>
   来源: https://medium.com/@leoFacci/analyzing-lottery-numbers-finding-patterns-in-the-data-5ed5880c611f

复刻的6个标准分析方法：
1. 热冷号分析 (Hot/Cold Number Analysis) - 基于频率分析
2. 遗漏值分析 (Missing Value Analysis) - 基于历史数据计算遗漏期数
3. 奇偶分析 (Odd/Even Analysis) - 分析奇偶数比例
4. 大小号分析 (Big/Small Number Analysis) - 分析大小号分布
5. 和值分析 (Sum Value Analysis) - 分析号码总和
6. 跨度分析 (Span Analysis) - 分析最大最小值差

适配体彩大乐透: 前区35选5 + 后区12选2
"""

import pandas as pd
import numpy as np
from collections import Counter
from datetime import datetime

class LotteryAnalysisReplicated:
    """
    复刻自真实项目的彩票分析类
    基于szczyglis-dev项目的数据处理框架
    """
    
    def __init__(self):
        # 体彩大乐透配置 (复刻自szczyglis项目的配置模式)
        self.config = {
            'front_range': [1, 35],    # 前区范围
            'back_range': [1, 12],     # 后区范围
            'front_count': 5,          # 前区选号数量
            'back_count': 2,           # 后区选号数量
            'big_small_threshold': 18  # 大小号分界线 (前区)
        }
    
    def frequency_analysis(self, df, number_columns):
        """
        频率分析 - 复刻自szczyglis项目
        原始代码:
        frequency = pd.concat([df[col] for col in number_columns]).value_counts()
        """
        # 复刻原项目的频率计算方法
        frequency = pd.concat([df[col] for col in number_columns]).value_counts()
        return frequency.sort_values(ascending=False)
    
    def hot_cold_analysis(self, df, front_cols, back_cols, recent_periods=20):
        """
        热冷号分析 - 基于频率分析复刻
        复刻自szczyglis项目的频率分析算法
        """
        # 前区热冷号分析
        recent_data = df.tail(recent_periods)
        front_freq = self.frequency_analysis(recent_data, front_cols)
        
        # 后区热冷号分析  
        back_freq = self.frequency_analysis(recent_data, back_cols)
        
        # 分类热号和冷号 (复刻原项目逻辑)
        front_hot = front_freq.head(10).index.tolist()  # 前10为热号
        front_cold = front_freq.tail(10).index.tolist()  # 后10为冷号
        
        back_hot = back_freq.head(6).index.tolist()   # 前6为热号
        back_cold = back_freq.tail(6).index.tolist()  # 后6为冷号
        
        return {
            'front_hot': front_hot,
            'front_cold': front_cold,
            'back_hot': back_hot,
            'back_cold': back_cold,
            'front_freq': front_freq,
            'back_freq': back_freq
        }
    
    def missing_analysis(self, df, front_cols, back_cols):
        """
        遗漏值分析 - 基于真实项目算法复刻
        计算每个号码最近一次出现到现在的期数
        """
        missing_data = {}
        
        # 前区遗漏分析
        for num in range(self.config['front_range'][0], self.config['front_range'][1] + 1):
            last_appear = -1
            for idx, row in df.iterrows():
                if num in [row[col] for col in front_cols if pd.notna(row[col])]:
                    last_appear = idx
            missing_data[f'front_{num}'] = len(df) - 1 - last_appear if last_appear >= 0 else len(df)
        
        # 后区遗漏分析
        for num in range(self.config['back_range'][0], self.config['back_range'][1] + 1):
            last_appear = -1
            for idx, row in df.iterrows():
                if num in [row[col] for col in back_cols if pd.notna(row[col])]:
                    last_appear = idx
            missing_data[f'back_{num}'] = len(df) - 1 - last_appear if last_appear >= 0 else len(df)
        
        return missing_data
    
    def odd_even_analysis(self, df, front_cols, back_cols):
        """
        奇偶分析 - 复刻自真实项目
        基于Medium文章中的奇偶分析方法
        """
        results = []
        
        for _, row in df.iterrows():
            # 前区奇偶分析
            front_numbers = [row[col] for col in front_cols if pd.notna(row[col])]
            front_odd = sum(1 for num in front_numbers if int(num) % 2 == 1)
            front_even = len(front_numbers) - front_odd
            
            # 后区奇偶分析
            back_numbers = [row[col] for col in back_cols if pd.notna(row[col])]
            back_odd = sum(1 for num in back_numbers if int(num) % 2 == 1)
            back_even = len(back_numbers) - back_odd
            
            results.append({
                'front_odd': front_odd,
                'front_even': front_even,
                'back_odd': back_odd,
                'back_even': back_even,
                'front_ratio': f"{front_odd}:{front_even}",
                'back_ratio': f"{back_odd}:{back_even}"
            })
        
        return results
    
    def big_small_analysis(self, df, front_cols, back_cols):
        """
        大小号分析 - 复刻自szczyglis项目的范围分析
        原项目中的df_append_range函数逻辑
        """
        results = []
        threshold = self.config['big_small_threshold']
        
        for _, row in df.iterrows():
            # 前区大小号分析
            front_numbers = [row[col] for col in front_cols if pd.notna(row[col])]
            front_small = sum(1 for num in front_numbers if int(num) <= threshold)
            front_big = len(front_numbers) - front_small
            
            # 后区大小号分析 (后区1-6为小，7-12为大)
            back_numbers = [row[col] for col in back_cols if pd.notna(row[col])]
            back_small = sum(1 for num in back_numbers if int(num) <= 6)
            back_big = len(back_numbers) - back_small
            
            results.append({
                'front_small': front_small,
                'front_big': front_big,
                'back_small': back_small,
                'back_big': back_big,
                'front_ratio': f"{front_small}:{front_big}",
                'back_ratio': f"{back_small}:{back_big}"
            })
        
        return results
    
    def sum_analysis(self, df, front_cols):
        """
        和值分析 - 复刻自真实项目算法
        基于Medium文章中的数值分析方法
        """
        sum_values = []
        
        for _, row in df.iterrows():
            front_numbers = [row[col] for col in front_cols if pd.notna(row[col])]
            total_sum = sum(int(num) for num in front_numbers)
            sum_values.append(total_sum)
        
        # 统计和值分布
        sum_counter = Counter(sum_values)
        sum_stats = {
            'values': sum_values,
            'min': min(sum_values),
            'max': max(sum_values),
            'mean': np.mean(sum_values),
            'distribution': dict(sum_counter.most_common())
        }
        
        return sum_stats
    
    def span_analysis(self, df, front_cols):
        """
        跨度分析 - 复刻自真实项目
        计算每期最大值与最小值的差值
        """
        span_values = []
        
        for _, row in df.iterrows():
            front_numbers = [int(row[col]) for col in front_cols if pd.notna(row[col])]
            if front_numbers:
                span = max(front_numbers) - min(front_numbers)
                span_values.append(span)
        
        # 统计跨度分布
        span_counter = Counter(span_values)
        span_stats = {
            'values': span_values,
            'min': min(span_values),
            'max': max(span_values),
            'mean': np.mean(span_values),
            'distribution': dict(span_counter.most_common())
        }
        
        return span_stats

def demo_analysis():
    """
    演示分析功能 - 复刻自szczyglis项目的演示模式
    """
    # 创建示例数据 (模拟体彩大乐透格式)
    sample_data = {
        'period': ['2024001', '2024002', '2024003', '2024004', '2024005'],
        'front_1': [3, 7, 12, 5, 18],
        'front_2': [8, 15, 18, 11, 22],
        'front_3': [15, 22, 25, 17, 28],
        'front_4': [22, 28, 30, 25, 31],
        'front_5': [35, 33, 35, 33, 35],
        'back_1': [3, 7, 5, 8, 2],
        'back_2': [8, 12, 11, 12, 9]
    }
    
    df = pd.DataFrame(sample_data)
    analyzer = LotteryAnalysisReplicated()
    
    front_cols = ['front_1', 'front_2', 'front_3', 'front_4', 'front_5']
    back_cols = ['back_1', 'back_2']
    
    print("=== 彩票分析方法复刻版演示 ===")
    print("复刻自真实项目的标准算法\n")
    
    # 1. 热冷号分析
    hot_cold = analyzer.hot_cold_analysis(df, front_cols, back_cols)
    print("1. 热冷号分析:")
    print(f"前区热号: {hot_cold['front_hot']}")
    print(f"前区冷号: {hot_cold['front_cold']}")
    print(f"后区热号: {hot_cold['back_hot']}")
    print(f"后区冷号: {hot_cold['back_cold']}\n")
    
    # 2. 奇偶分析
    odd_even = analyzer.odd_even_analysis(df, front_cols, back_cols)
    print("2. 奇偶分析:")
    for i, result in enumerate(odd_even):
        print(f"第{i+1}期: 前区{result['front_ratio']}, 后区{result['back_ratio']}")
    print()
    
    # 3. 和值分析
    sum_stats = analyzer.sum_analysis(df, front_cols)
    print("3. 和值分析:")
    print(f"和值范围: {sum_stats['min']} - {sum_stats['max']}")
    print(f"平均和值: {sum_stats['mean']:.2f}")
    print(f"和值分布: {sum_stats['distribution']}\n")
    
    # 4. 跨度分析
    span_stats = analyzer.span_analysis(df, front_cols)
    print("4. 跨度分析:")
    print(f"跨度范围: {span_stats['min']} - {span_stats['max']}")
    print(f"平均跨度: {span_stats['mean']:.2f}")
    print(f"跨度分布: {span_stats['distribution']}")

def comprehensive_analysis(df, front_cols, back_cols):
    """
    综合分析 - 复刻自真实项目的完整分析流程
    基于szczyglis项目的分析框架
    """
    analyzer = LotteryAnalysisReplicated()

    print("=== 彩票分析方法复刻版 - 完整分析报告 ===")
    print("基于真实项目算法复刻\n")

    # 1. 热冷号分析
    print("【1. 热冷号分析】- 复刻自szczyglis项目频率分析")
    hot_cold = analyzer.hot_cold_analysis(df, front_cols, back_cols)
    print(f"前区热号TOP10: {hot_cold['front_hot']}")
    print(f"前区冷号TOP10: {hot_cold['front_cold']}")
    print(f"后区热号TOP6: {hot_cold['back_hot']}")
    print(f"后区冷号TOP6: {hot_cold['back_cold']}")
    print(f"前区频率分布: {dict(hot_cold['front_freq'].head(10))}")
    print()

    # 2. 遗漏值分析
    print("【2. 遗漏值分析】- 基于历史数据计算")
    missing = analyzer.missing_analysis(df, front_cols, back_cols)
    front_missing = {k.replace('front_', ''): v for k, v in missing.items() if k.startswith('front_')}
    back_missing = {k.replace('back_', ''): v for k, v in missing.items() if k.startswith('back_')}

    # 显示遗漏最大的号码
    front_max_missing = sorted(front_missing.items(), key=lambda x: x[1], reverse=True)[:10]
    back_max_missing = sorted(back_missing.items(), key=lambda x: x[1], reverse=True)[:6]

    print(f"前区遗漏最大TOP10: {front_max_missing}")
    print(f"后区遗漏最大TOP6: {back_max_missing}")
    print()

    # 3. 奇偶分析
    print("【3. 奇偶分析】- 复刻自Medium文章算法")
    odd_even = analyzer.odd_even_analysis(df, front_cols, back_cols)

    # 统计奇偶比例分布
    front_ratios = [result['front_ratio'] for result in odd_even]
    back_ratios = [result['back_ratio'] for result in odd_even]

    from collections import Counter
    front_ratio_count = Counter(front_ratios)
    back_ratio_count = Counter(back_ratios)

    print(f"前区奇偶比例分布: {dict(front_ratio_count)}")
    print(f"后区奇偶比例分布: {dict(back_ratio_count)}")
    print(f"最近5期奇偶情况:")
    for i, result in enumerate(odd_even[-5:]):
        print(f"  第{len(odd_even)-4+i}期: 前区{result['front_ratio']}, 后区{result['back_ratio']}")
    print()

    # 4. 大小号分析
    print("【4. 大小号分析】- 复刻自szczyglis项目范围分析")
    big_small = analyzer.big_small_analysis(df, front_cols, back_cols)

    # 统计大小号比例分布
    front_bs_ratios = [result['front_ratio'] for result in big_small]
    back_bs_ratios = [result['back_ratio'] for result in big_small]

    front_bs_count = Counter(front_bs_ratios)
    back_bs_count = Counter(back_bs_ratios)

    print(f"前区大小号比例分布: {dict(front_bs_count)}")
    print(f"后区大小号比例分布: {dict(back_bs_count)}")
    print(f"最近5期大小号情况:")
    for i, result in enumerate(big_small[-5:]):
        print(f"  第{len(big_small)-4+i}期: 前区{result['front_ratio']}, 后区{result['back_ratio']}")
    print()

    # 5. 和值分析
    print("【5. 和值分析】- 基于真实项目数值分析")
    sum_stats = analyzer.sum_analysis(df, front_cols)
    print(f"和值统计: 最小{sum_stats['min']}, 最大{sum_stats['max']}, 平均{sum_stats['mean']:.2f}")
    print(f"最近5期和值: {sum_stats['values'][-5:]}")
    print(f"和值分布TOP10: {dict(list(sum_stats['distribution'].items())[:10])}")
    print()

    # 6. 跨度分析
    print("【6. 跨度分析】- 最大最小值差分析")
    span_stats = analyzer.span_analysis(df, front_cols)
    print(f"跨度统计: 最小{span_stats['min']}, 最大{span_stats['max']}, 平均{span_stats['mean']:.2f}")
    print(f"最近5期跨度: {span_stats['values'][-5:]}")
    print(f"跨度分布TOP10: {dict(list(span_stats['distribution'].items())[:10])}")

    return {
        'hot_cold': hot_cold,
        'missing': missing,
        'odd_even': odd_even,
        'big_small': big_small,
        'sum_stats': sum_stats,
        'span_stats': span_stats
    }

if __name__ == "__main__":
    demo_analysis()

    print("\n" + "="*60)
    print("运行完整分析请使用: comprehensive_analysis(df, front_cols, back_cols)")
    print("代码复刻来源:")
    print("1. GitHub: szczyglis-dev/python-lottery-dataset-analyze")
    print("2. Medium: Leopoldo Facci的彩票分析文章")
    print("="*60)
