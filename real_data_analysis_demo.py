"""
真实大乐透数据分析演示
====================

使用从体彩官方API获取的真实开奖数据
结合复刻的标准分析算法进行实际分析

数据来源: 体彩官方API (100条真实开奖记录)
分析方法: 复刻自真实项目的6种标准算法
"""

import pandas as pd
import numpy as np
from lottery_analysis_replicated import LotteryAnalysisReplicated, comprehensive_analysis

def load_real_data():
    """加载真实的大乐透数据"""
    try:
        df = pd.read_csv('lottery_data.csv')
        print(f"✅ 成功加载真实数据: {len(df)}条记录")
        print(f"📅 数据范围: {df.iloc[-1]['draw_date']} 至 {df.iloc[0]['draw_date']}")
        print(f"🎯 期号范围: {df.iloc[-1]['period']} 至 {df.iloc[0]['period']}")
        return df
    except FileNotFoundError:
        print("❌ 未找到数据文件，请先运行 lottery_data_updater.py 获取数据")
        return None
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def analyze_real_data():
    """分析真实大乐透数据"""
    print("🎲 真实大乐透数据分析演示")
    print("=" * 60)
    
    # 加载真实数据
    df = load_real_data()
    if df is None:
        return
    
    # 定义列名
    front_cols = ['front_1', 'front_2', 'front_3', 'front_4', 'front_5']
    back_cols = ['back_1', 'back_2']
    
    print("\n🔍 数据预览 (最近5期):")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        print(f"  {row['period']}: {row['draw_result']} ({row['draw_date']})")
    
    print("\n" + "="*60)
    print("🧮 开始使用复刻算法分析真实数据...")
    print("="*60)
    
    # 运行完整分析
    results = comprehensive_analysis(df, front_cols, back_cols)
    
    print("\n" + "="*60)
    print("📊 真实数据分析总结")
    print("="*60)
    
    # 热号分析总结
    hot_cold = results['hot_cold']
    print(f"\n🔥 前区热号TOP5: {hot_cold['front_hot'][:5]}")
    print(f"❄️ 前区冷号TOP5: {hot_cold['front_cold'][:5]}")
    print(f"🔥 后区热号TOP3: {hot_cold['back_hot'][:3]}")
    print(f"❄️ 后区冷号TOP3: {hot_cold['back_cold'][:3]}")
    
    # 和值分析总结
    sum_stats = results['sum_stats']
    print(f"\n📈 和值分析:")
    print(f"   平均和值: {sum_stats['mean']:.1f}")
    print(f"   和值范围: {sum_stats['min']} - {sum_stats['max']}")
    print(f"   最近5期和值: {sum_stats['values'][:5]}")
    
    # 跨度分析总结
    span_stats = results['span_stats']
    print(f"\n📏 跨度分析:")
    print(f"   平均跨度: {span_stats['mean']:.1f}")
    print(f"   跨度范围: {span_stats['min']} - {span_stats['max']}")
    print(f"   最近5期跨度: {span_stats['values'][:5]}")
    
    # 奇偶分析总结
    odd_even = results['odd_even']
    from collections import Counter
    front_ratios = [result['front_ratio'] for result in odd_even]
    front_ratio_count = Counter(front_ratios)
    print(f"\n⚖️ 前区奇偶比例分布:")
    for ratio, count in front_ratio_count.most_common(5):
        print(f"   {ratio}: {count}次 ({count/len(odd_even)*100:.1f}%)")
    
    # 大小号分析总结
    big_small = results['big_small']
    front_bs_ratios = [result['front_ratio'] for result in big_small]
    front_bs_count = Counter(front_bs_ratios)
    print(f"\n📊 前区大小号比例分布:")
    for ratio, count in front_bs_count.most_common(5):
        print(f"   {ratio}: {count}次 ({count/len(big_small)*100:.1f}%)")
    
    return results

def generate_prediction_reference(results):
    """基于分析结果生成参考建议"""
    print("\n" + "="*60)
    print("🎯 基于真实数据的参考建议")
    print("="*60)
    print("⚠️  注意: 以下仅为数据分析结果，彩票具有随机性，不保证中奖")
    
    hot_cold = results['hot_cold']
    
    print(f"\n📋 号码选择参考:")
    print(f"🔥 前区热号推荐: {hot_cold['front_hot'][:10]}")
    print(f"❄️ 前区冷号关注: {hot_cold['front_cold'][:10]}")
    print(f"🔥 后区热号推荐: {hot_cold['back_hot'][:6]}")
    print(f"❄️ 后区冷号关注: {hot_cold['back_cold'][:6]}")
    
    # 和值建议
    sum_stats = results['sum_stats']
    avg_sum = sum_stats['mean']
    print(f"\n📈 和值参考:")
    print(f"   历史平均和值: {avg_sum:.1f}")
    print(f"   建议和值范围: {int(avg_sum-15)} - {int(avg_sum+15)}")
    
    # 跨度建议
    span_stats = results['span_stats']
    avg_span = span_stats['mean']
    print(f"\n📏 跨度参考:")
    print(f"   历史平均跨度: {avg_span:.1f}")
    print(f"   建议跨度范围: {int(avg_span-5)} - {int(avg_span+5)}")
    
    print(f"\n💡 分析方法说明:")
    print(f"   ✅ 使用复刻自真实项目的标准算法")
    print(f"   ✅ 基于{len(results['hot_cold']['front_freq'])}条真实开奖数据")
    print(f"   ✅ 热冷号分析基于最近20期数据")
    print(f"   ✅ 所有统计数据均来自官方开奖结果")

def main():
    """主函数"""
    print("🎲 真实大乐透数据分析系统")
    print("基于体彩官方API数据 + 复刻标准算法")
    print("="*60)
    
    # 分析真实数据
    results = analyze_real_data()
    
    if results:
        # 生成参考建议
        generate_prediction_reference(results)
        
        print("\n" + "="*60)
        print("✅ 分析完成！")
        print("📁 详细数据已保存在 lottery_data.csv")
        print("🔄 运行 lottery_data_updater.py 可更新最新数据")
        print("📊 重新运行此脚本可获得最新分析结果")
        print("="*60)

if __name__ == "__main__":
    main()
