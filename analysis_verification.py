"""
分析结果验证脚本
================

手动验证分析结果的准确性和合理性
检查热冷号、和值、跨度等关键指标
"""

import pandas as pd
from collections import Counter

def verify_analysis():
    """验证分析结果"""
    print("🔍 分析结果验证")
    print("="*50)
    
    # 加载数据
    df = pd.read_csv('lottery_data.csv')
    print(f"✅ 数据加载成功: {len(df)}条记录")
    
    # 1. 验证热号分析
    print("\n【1. 热号分析验证】")
    front_cols = ['front_1', 'front_2', 'front_3', 'front_4', 'front_5']
    back_cols = ['back_1', 'back_2']
    
    # 统计前区号码频率
    front_numbers = []
    for col in front_cols:
        front_numbers.extend(df[col].tolist())
    
    front_freq = Counter(front_numbers)
    print(f"前区号码总数: {len(front_numbers)} (应该是 {len(df)*5})")
    print(f"前区热号TOP5: {[num for num, count in front_freq.most_common(5)]}")
    print(f"前区热号频次: {[count for num, count in front_freq.most_common(5)]}")
    
    # 统计后区号码频率
    back_numbers = []
    for col in back_cols:
        back_numbers.extend(df[col].tolist())
    
    back_freq = Counter(back_numbers)
    print(f"后区号码总数: {len(back_numbers)} (应该是 {len(df)*2})")
    print(f"后区热号TOP3: {[num for num, count in back_freq.most_common(3)]}")
    print(f"后区热号频次: {[count for num, count in back_freq.most_common(3)]}")
    
    # 2. 验证和值分析
    print("\n【2. 和值分析验证】")
    sums = []
    for i, row in df.iterrows():
        sum_val = sum([row[col] for col in front_cols])
        sums.append(sum_val)
    
    print(f"和值范围: {min(sums)} - {max(sums)}")
    print(f"平均和值: {sum(sums)/len(sums):.2f}")
    print(f"最近5期和值: {sums[:5]}")

    # 3. 验证跨度分析
    print("\n【3. 跨度分析验证】")
    spans = []
    for i, row in df.iterrows():
        front_nums = [row[col] for col in front_cols]
        span = max(front_nums) - min(front_nums)
        spans.append(span)

    print(f"跨度范围: {min(spans)} - {max(spans)}")
    print(f"平均跨度: {sum(spans)/len(spans):.2f}")
    print(f"最近5期跨度: {spans[:5]}")
    
    # 4. 验证奇偶分析
    print("\n【4. 奇偶分析验证】")
    odd_even_ratios = []
    for i, row in df.iterrows():
        front_nums = [row[col] for col in front_cols]
        odd_count = sum(1 for num in front_nums if num % 2 == 1)
        even_count = 5 - odd_count
        ratio = f"{odd_count}:{even_count}"
        odd_even_ratios.append(ratio)
    
    ratio_count = Counter(odd_even_ratios)
    print("前区奇偶比例分布:")
    for ratio, count in ratio_count.most_common():
        print(f"  {ratio}: {count}次 ({count/len(df)*100:.1f}%)")
    
    # 5. 验证大小号分析 (前区1-17小号，18-35大号)
    print("\n【5. 大小号分析验证】")
    big_small_ratios = []
    for i, row in df.iterrows():
        front_nums = [row[col] for col in front_cols]
        small_count = sum(1 for num in front_nums if num <= 17)
        big_count = 5 - small_count
        ratio = f"{big_count}:{small_count}"
        big_small_ratios.append(ratio)
    
    bs_ratio_count = Counter(big_small_ratios)
    print("前区大小号比例分布:")
    for ratio, count in bs_ratio_count.most_common():
        print(f"  {ratio}: {count}次 ({count/len(df)*100:.1f}%)")
    
    # 6. 数据合理性检查
    print("\n【6. 数据合理性检查】")
    
    # 检查前区号码范围
    all_front = []
    for col in front_cols:
        all_front.extend(df[col].tolist())
    
    front_min, front_max = min(all_front), max(all_front)
    print(f"前区号码范围: {front_min}-{front_max} (标准: 1-35)")
    
    # 检查后区号码范围
    all_back = []
    for col in back_cols:
        all_back.extend(df[col].tolist())
    
    back_min, back_max = min(all_back), max(all_back)
    print(f"后区号码范围: {back_min}-{back_max} (标准: 1-12)")
    
    # 检查是否有重复号码
    duplicate_count = 0
    for i, row in df.iterrows():
        front_nums = [row[col] for col in front_cols]
        if len(set(front_nums)) != 5:
            duplicate_count += 1
    
    print(f"前区重复号码期数: {duplicate_count} (应该为0)")
    
    # 7. 验证最新几期的具体数据
    print("\n【7. 最新5期详细验证】")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        front_nums = [row[col] for col in front_cols]
        back_nums = [row[col] for col in back_cols]
        
        # 计算和值
        sum_val = sum(front_nums)
        # 计算跨度
        span = max(front_nums) - min(front_nums)
        # 计算奇偶
        odd_count = sum(1 for num in front_nums if num % 2 == 1)
        # 计算大小号
        big_count = sum(1 for num in front_nums if num > 17)
        
        print(f"  期号{row['period']}: {row['draw_result']}")
        print(f"    和值:{sum_val}, 跨度:{span}, 奇偶:{odd_count}:{5-odd_count}, 大小:{big_count}:{5-big_count}")
    
    print("\n✅ 验证完成！")

if __name__ == "__main__":
    verify_analysis()
