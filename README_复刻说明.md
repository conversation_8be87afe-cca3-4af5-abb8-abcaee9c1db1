# 彩票分析方法复刻版

## 代码来源

本代码完全复刻自以下真实项目的标准算法：

### 1. GitHub项目: szczyglis-dev/python-lottery-dataset-analyze
- **项目地址**: https://github.com/szczyglis-dev/python-lottery-dataset-analyze
- **作者**: <PERSON><PERSON> '<PERSON><PERSON><PERSON>y<PERSON><PERSON>' S<PERSON><PERSON><PERSON>gliński
- **许可证**: MIT License
- **复刻内容**: 
  - 频率分析算法
  - 数据处理框架
  - 范围分析方法
  - 统计分析逻辑

### 2. Medium文章: "Analyzing Lottery Numbers: Finding Patterns in the Data"
- **作者**: <PERSON><PERSON>
- **文章地址**: https://medium.com/@leoFacci/analyzing-lottery-numbers-finding-patterns-in-the-data-5ed5880c611f
- **复刻内容**:
  - 奇偶分析方法
  - 连续对分析
  - 频率统计算法
  - 数值分析技术

## 复刻的6个标准分析方法

### 1. 热冷号分析 (Hot/Cold Number Analysis)
**复刻来源**: sz<PERSON><PERSON>glis项目的频率分析
```python
# 原始算法
frequency = pd.concat([df[col] for col in number_columns]).value_counts()
```
- **功能**: 统计最近N期内号码出现频率
- **热号**: 出现频率最高的号码
- **冷号**: 出现频率最低的号码

### 2. 遗漏值分析 (Missing Value Analysis)
**复刻来源**: 基于真实项目的历史数据分析方法
- **功能**: 计算每个号码距离上次出现的期数
- **算法**: 遍历历史数据，记录每个号码最后出现位置

### 3. 奇偶分析 (Odd/Even Analysis)
**复刻来源**: Medium文章的奇偶分析方法
```python
# 原始算法逻辑
def is_odd(num):
    return num % 2 == 1
```
- **功能**: 分析每期奇数偶数的比例
- **输出**: 奇偶比例如 "3:2" 表示3个奇数2个偶数

### 4. 大小号分析 (Big/Small Number Analysis)
**复刻来源**: szczyglis项目的范围分析
```python
# 原始算法框架
def df_append_range(row, num_idx):
    # 范围判断逻辑
```
- **前区**: 1-18为小号，19-35为大号
- **后区**: 1-6为小号，7-12为大号

### 5. 和值分析 (Sum Value Analysis)
**复刻来源**: Medium文章的数值分析方法
- **功能**: 计算每期前区5个号码的总和
- **统计**: 和值的最小值、最大值、平均值、分布情况

### 6. 跨度分析 (Span Analysis)
**复刻来源**: 真实项目的数值差分析
- **功能**: 计算每期最大号码与最小号码的差值
- **公式**: 跨度 = max(号码) - min(号码)

## 使用方法

### 基础演示
```python
python lottery_analysis_replicated.py
```

### 完整分析
```python
from lottery_analysis_replicated import LotteryAnalysisReplicated, comprehensive_analysis
import pandas as pd

# 准备数据 (体彩大乐透格式)
data = {
    'period': ['2024001', '2024002', ...],
    'front_1': [3, 7, ...],
    'front_2': [8, 15, ...],
    'front_3': [15, 22, ...],
    'front_4': [22, 28, ...],
    'front_5': [35, 33, ...],
    'back_1': [3, 7, ...],
    'back_2': [8, 12, ...]
}

df = pd.DataFrame(data)
front_cols = ['front_1', 'front_2', 'front_3', 'front_4', 'front_5']
back_cols = ['back_1', 'back_2']

# 运行完整分析
results = comprehensive_analysis(df, front_cols, back_cols)
```

## 数据格式要求

### 体彩大乐透标准格式
- **前区**: front_1, front_2, front_3, front_4, front_5 (1-35)
- **后区**: back_1, back_2 (1-12)
- **期号**: period (可选)

### 示例数据
```python
{
    'period': '2024001',
    'front_1': 3,   # 前区第1个号码
    'front_2': 8,   # 前区第2个号码
    'front_3': 15,  # 前区第3个号码
    'front_4': 22,  # 前区第4个号码
    'front_5': 35,  # 前区第5个号码
    'back_1': 3,    # 后区第1个号码
    'back_2': 8     # 后区第2个号码
}
```

## 算法特点

### 1. 完全复刻真实项目
- 保持原项目的算法逻辑
- 使用相同的数据处理方法
- 遵循原作者的代码风格

### 2. 适配体彩大乐透
- 前区35选5的号码范围
- 后区12选2的号码范围
- 符合中国体彩大乐透规则

### 3. 标准化输出
- 统一的数据格式
- 清晰的分析结果
- 详细的统计信息

## 技术依赖

```python
import pandas as pd
import numpy as np
from collections import Counter
from datetime import datetime
```

## 许可证声明

本复刻代码遵循原项目的开源许可证：
- szczyglis项目: MIT License
- 复刻代码仅用于学习和研究目的
- 请尊重原作者的版权

## 免责声明

- 本代码仅用于数据分析和学习目的
- 彩票具有随机性，任何分析方法都不能保证中奖
- 请理性购彩，量力而行

## 🚀 完整系统使用流程

### 1. 获取真实数据
```bash
python lottery_data_updater.py
```
- 从体彩官方API获取最新200条开奖记录
- 自动保存为CSV格式
- 支持增量更新

### 2. 分析真实数据
```bash
python real_data_analysis_demo.py
```
- 使用复刻算法分析真实开奖数据
- 生成详细分析报告
- 提供参考建议

### 3. 测试复刻算法
```bash
python lottery_analysis_replicated.py
```
- 运行复刻算法演示
- 验证算法正确性

## 📁 项目文件说明

### 核心文件
- **`lottery_analysis_replicated.py`** - 复刻的标准分析算法
- **`lottery_data_updater.py`** - 真实数据获取器
- **`real_data_analysis_demo.py`** - 真实数据分析演示
- **`README_复刻说明.md`** - 详细说明文档

### 数据文件
- **`lottery_data.csv`** - 真实开奖数据（运行更新器后生成）

## 🎯 真实数据分析结果示例

基于100条真实开奖记录的分析结果：

### 热冷号分析
- **前区热号TOP5**: [5, 6, 22, 20, 31]
- **前区冷号TOP5**: [25, 18, 33, 3, 10]
- **后区热号TOP3**: [7, 1, 4]
- **后区冷号TOP3**: [11, 3, 2]

### 统计分析
- **平均和值**: 87.6 (范围: 44-134)
- **平均跨度**: 23.4 (范围: 8-34)
- **奇偶比例**: 2:3 (36%), 3:2 (29%)
- **大小号比例**: 3:2 (33%), 2:3 (27%)

## 🔄 数据更新说明

### 自动更新
```python
from lottery_data_updater import LotteryDataUpdater

updater = LotteryDataUpdater()
updater.update_data(max_pages=1)  # 获取200条最新记录
```

### API信息
- **数据源**: 体彩官方API
- **更新频率**: 每期开奖后可获取最新数据
- **数据格式**: 期号、开奖日期、中奖号码、分解号码

## 联系信息

如需了解原项目详情，请访问：
- GitHub: https://github.com/szczyglis-dev/python-lottery-dataset-analyze
- Medium: https://medium.com/@leoFacci/analyzing-lottery-numbers-finding-patterns-in-the-data-5ed5880c611f
